<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="6">
            <item index="0" class="java.lang.String" itemvalue="dash" />
            <item index="1" class="java.lang.String" itemvalue="dash_core_components" />
            <item index="2" class="java.lang.String" itemvalue="dash_table" />
            <item index="3" class="java.lang.String" itemvalue="dash_html_components" />
            <item index="4" class="java.lang.String" itemvalue="dash_bootstrap_components" />
            <item index="5" class="java.lang.String" itemvalue="skimage" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>