#!/usr/bin/env python3
"""
DysplaciaNet Model Evaluation and Visualization Script

This script loads the DysplaciaNet model, evaluates its performance,
and provides visualization examples with annotations.

Author: Generated for DysplaciaNet Interpretability Study
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Patch
import keras
from keras import models
from keras.preprocessing import image
import cv2
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report
import seaborn as sns
from pathlib import Path
import json

# Set random seed for reproducibility
np.random.seed(42)

class DysplaciaNetEvaluator:
    """
    A class to handle DysplaciaNet model loading, evaluation, and visualization.
    """
    
    def __init__(self, model_path='model/final_model.json', weights_path='model/final_model_weights.h5'):
        """
        Initialize the evaluator with model paths.
        
        Args:
            model_path (str): Path to the model JSON file
            weights_path (str): Path to the model weights file
        """
        self.model_path = model_path
        self.weights_path = weights_path
        self.model = None
        self.img_height = 299
        self.img_width = 299
        
        # Example images from the codebase
        self.example_images = [
            "TD_BNE_2249981.jpg", "TD_BNE_2256328.jpg", "TD_SNE_741897.jpg",
            "TN_SNE_118039.jpg", "TN_SNE_14872661.jpg", "TN_SNE_14872673.jpg",
            "ZD_BNE_2433069.jpg", "ZD_BNE_2433092.jpg", "ZD_BNE_2433255.jpg",
            "ZN_SNE_4198926.jpg", "ZN_SNE_4198932.jpg", "ZN_SNE_4198946.jpg"
        ]
        
        # Corresponding labels (0 = dysplastic, 1 = normal)
        self.example_labels = [0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1]
        
        # Annotation scores from the original data
        self.annotation_scores = [7, 6, 5, -2, 0, 0, 4, 5, 6, -3, -1, -3]
        
    def load_model(self):
        """
        Load the DysplaciaNet model from JSON and weights files.
        
        Returns:
            keras.Model: Loaded and compiled model
        """
        try:
            # Load model architecture from JSON
            with open(self.model_path, 'r') as json_file:
                json_config = json_file.read()
            
            self.model = keras.models.model_from_json(json_config)
            
            # Load weights
            self.model.load_weights(self.weights_path)
            
            # Compile model
            self.model.compile(
                loss='binary_crossentropy',
                optimizer='adam',
                metrics=['accuracy']
            )
            
            print("Model loaded successfully!")
            print(f"Model input shape: {self.model.input_shape}")
            print(f"Model output shape: {self.model.output_shape}")
            
            return self.model
            
        except Exception as e:
            print(f"Error loading model: {e}")
            return None
    
    def print_model_summary(self):
        """Print detailed model architecture summary."""
        if self.model is None:
            print("Model not loaded. Please call load_model() first.")
            return
        
        print("\n" + "="*50)
        print("DYSPLACIANET MODEL ARCHITECTURE")
        print("="*50)
        self.model.summary()
        
        # Count parameters
        total_params = self.model.count_params()
        print(f"\nTotal parameters: {total_params:,}")
        
    def path_to_image_tensor(self, path):
        """
        Convert image path to preprocessed tensor.
        
        Args:
            path (str): Path to image file
            
        Returns:
            numpy.ndarray: Preprocessed image tensor
        """
        try:
            img = image.load_img(path, target_size=(self.img_height, self.img_width))
            img_tensor = image.img_to_array(img)
            img_tensor = np.expand_dims(img_tensor, axis=0)
            img_tensor /= 255.0  # Normalize to [0, 1]
            return img_tensor
        except Exception as e:
            print(f"Error loading image {path}: {e}")
            return None
    
    def make_prediction(self, image_path, verbose=True):
        """
        Make prediction on a single image.
        
        Args:
            image_path (str): Path to image file
            verbose (bool): Whether to print prediction details
            
        Returns:
            tuple: (prediction_probability, predicted_class)
        """
        if self.model is None:
            print("Model not loaded. Please call load_model() first.")
            return None, None
        
        img_tensor = self.path_to_image_tensor(image_path)
        if img_tensor is None:
            return None, None
        
        prediction_prob = self.model.predict(img_tensor, verbose=0)[0][0]
        predicted_class = 1 if prediction_prob > 0.5 else 0
        
        if verbose:
            class_name = "Normal" if predicted_class == 1 else "Dysplastic"
            confidence = prediction_prob if predicted_class == 1 else (1 - prediction_prob)
            print(f"Image: {os.path.basename(image_path)}")
            print(f"Prediction: {class_name} (confidence: {confidence:.3f})")
            print(f"Raw probability: {prediction_prob:.3f}")
        
        return prediction_prob, predicted_class

    def evaluate_on_examples(self, image_folder_path=None):
        """
        Evaluate model on example images and compute metrics.

        Args:
            image_folder_path (str): Path to folder containing example images

        Returns:
            dict: Dictionary containing evaluation metrics
        """
        if self.model is None:
            print("Model not loaded. Please call load_model() first.")
            return None

        if image_folder_path is None:
            print("Please provide image_folder_path to evaluate on example images.")
            return None

        predictions = []
        true_labels = []
        prediction_probs = []

        print("\n" + "="*50)
        print("EVALUATING ON EXAMPLE IMAGES")
        print("="*50)

        for i, (img_name, true_label) in enumerate(zip(self.example_images, self.example_labels)):
            img_path = os.path.join(image_folder_path, img_name)

            if os.path.exists(img_path):
                prob, pred = self.make_prediction(img_path, verbose=False)
                if prob is not None:
                    predictions.append(pred)
                    true_labels.append(true_label)
                    prediction_probs.append(prob)

                    # Print individual result
                    true_class = "Normal" if true_label == 1 else "Dysplastic"
                    pred_class = "Normal" if pred == 1 else "Dysplastic"
                    correct = "✓" if pred == true_label else "✗"
                    print(f"{i+1:2d}. {img_name:<20} | True: {true_class:<10} | Pred: {pred_class:<10} | {correct}")
            else:
                print(f"Image not found: {img_path}")

        if len(predictions) == 0:
            print("No valid predictions made.")
            return None

        # Calculate metrics
        accuracy = accuracy_score(true_labels, predictions)
        precision = precision_score(true_labels, predictions, average='binary')
        recall = recall_score(true_labels, predictions, average='binary')
        f1 = f1_score(true_labels, predictions, average='binary')

        metrics = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'predictions': predictions,
            'true_labels': true_labels,
            'prediction_probs': prediction_probs
        }

        print(f"\n" + "="*30)
        print("EVALUATION METRICS")
        print("="*30)
        print(f"Accuracy:  {accuracy:.3f}")
        print(f"Precision: {precision:.3f}")
        print(f"Recall:    {recall:.3f}")
        print(f"F1-Score:  {f1:.3f}")

        return metrics

    def plot_confusion_matrix(self, metrics):
        """
        Plot confusion matrix from evaluation metrics.

        Args:
            metrics (dict): Metrics dictionary from evaluate_on_examples
        """
        if metrics is None:
            print("No metrics provided.")
            return

        cm = confusion_matrix(metrics['true_labels'], metrics['predictions'])

        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=['Dysplastic', 'Normal'],
                   yticklabels=['Dysplastic', 'Normal'])
        plt.title('Confusion Matrix - DysplaciaNet')
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        plt.tight_layout()
        plt.show()

    def visualize_predictions_with_annotations(self, image_folder_path, num_examples=6):
        """
        Visualize predictions with annotations and scores.

        Args:
            image_folder_path (str): Path to folder containing images
            num_examples (int): Number of examples to display
        """
        if self.model is None:
            print("Model not loaded. Please call load_model() first.")
            return

        if image_folder_path is None:
            print("Please provide image_folder_path.")
            return

        # Select subset of examples
        indices = np.random.choice(len(self.example_images),
                                 min(num_examples, len(self.example_images)),
                                 replace=False)

        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()

        for i, idx in enumerate(indices):
            if i >= len(axes):
                break

            img_name = self.example_images[idx]
            true_label = self.example_labels[idx]
            annotation_score = self.annotation_scores[idx]

            img_path = os.path.join(image_folder_path, img_name)

            if os.path.exists(img_path):
                # Load and display image
                img = image.load_img(img_path, target_size=(self.img_width, self.img_height))
                axes[i].imshow(img)

                # Make prediction
                prob, pred = self.make_prediction(img_path, verbose=False)

                if prob is not None:
                    # Prepare labels
                    true_class = "Normal" if true_label == 1 else "Dysplastic"
                    pred_class = "Normal" if pred == 1 else "Dysplastic"
                    confidence = prob if pred == 1 else (1 - prob)

                    # Color coding: green for correct, red for incorrect
                    color = 'green' if pred == true_label else 'red'

                    # Title with prediction info
                    title = f"{img_name}\n"
                    title += f"True: {true_class} | Pred: {pred_class}\n"
                    title += f"Confidence: {confidence:.3f} | Score: {annotation_score}"

                    axes[i].set_title(title, fontsize=10, color=color, weight='bold')
                else:
                    axes[i].set_title(f"{img_name}\nPrediction failed", color='red')
            else:
                axes[i].text(0.5, 0.5, f"Image not found:\n{img_name}",
                           ha='center', va='center', transform=axes[i].transAxes)
                axes[i].set_title("Image not found", color='red')

            axes[i].axis('off')

        # Hide unused subplots
        for i in range(len(indices), len(axes)):
            axes[i].axis('off')

        plt.suptitle('DysplaciaNet Predictions with Annotations', fontsize=16, weight='bold')
        plt.tight_layout()
        plt.show()

    def plot_prediction_distribution(self, metrics):
        """
        Plot distribution of prediction probabilities.

        Args:
            metrics (dict): Metrics dictionary from evaluate_on_examples
        """
        if metrics is None:
            print("No metrics provided.")
            return

        probs = np.array(metrics['prediction_probs'])
        labels = np.array(metrics['true_labels'])

        plt.figure(figsize=(12, 5))

        # Subplot 1: Histogram of prediction probabilities
        plt.subplot(1, 2, 1)
        plt.hist(probs[labels == 0], alpha=0.7, label='Dysplastic (True)', bins=10, color='red')
        plt.hist(probs[labels == 1], alpha=0.7, label='Normal (True)', bins=10, color='blue')
        plt.axvline(x=0.5, color='black', linestyle='--', label='Decision Threshold')
        plt.xlabel('Prediction Probability')
        plt.ylabel('Count')
        plt.title('Distribution of Prediction Probabilities')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # Subplot 2: Scatter plot of predictions vs true labels
        plt.subplot(1, 2, 2)
        colors = ['red' if l == 0 else 'blue' for l in labels]
        plt.scatter(range(len(probs)), probs, c=colors, alpha=0.7)
        plt.axhline(y=0.5, color='black', linestyle='--', label='Decision Threshold')
        plt.xlabel('Sample Index')
        plt.ylabel('Prediction Probability')
        plt.title('Predictions vs True Labels')
        plt.legend(['Decision Threshold', 'Dysplastic', 'Normal'])
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    def generate_detailed_report(self, metrics, image_folder_path):
        """
        Generate a detailed evaluation report.

        Args:
            metrics (dict): Metrics dictionary from evaluate_on_examples
            image_folder_path (str): Path to image folder
        """
        if metrics is None:
            print("No metrics provided.")
            return

        print("\n" + "="*60)
        print("DETAILED DYSPLACIANET EVALUATION REPORT")
        print("="*60)

        print(f"Dataset: {len(metrics['true_labels'])} example images")
        print(f"Image folder: {image_folder_path}")
        print(f"Model architecture: CNN with {self.model.count_params():,} parameters")

        print(f"\nPerformance Metrics:")
        print(f"  Accuracy:  {metrics['accuracy']:.3f}")
        print(f"  Precision: {metrics['precision']:.3f}")
        print(f"  Recall:    {metrics['recall']:.3f}")
        print(f"  F1-Score:  {metrics['f1_score']:.3f}")

        # Classification report
        print(f"\nDetailed Classification Report:")
        print(classification_report(metrics['true_labels'], metrics['predictions'],
                                  target_names=['Dysplastic', 'Normal']))

        # Confusion matrix details
        cm = confusion_matrix(metrics['true_labels'], metrics['predictions'])
        tn, fp, fn, tp = cm.ravel()

        print(f"\nConfusion Matrix Breakdown:")
        print(f"  True Negatives (Dysplastic correctly identified):  {tn}")
        print(f"  False Positives (Normal misclassified as Dysplastic): {fp}")
        print(f"  False Negatives (Dysplastic misclassified as Normal): {fn}")
        print(f"  True Positives (Normal correctly identified):      {tp}")

        # Additional metrics
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0

        print(f"\nAdditional Metrics:")
        print(f"  Sensitivity (True Positive Rate): {sensitivity:.3f}")
        print(f"  Specificity (True Negative Rate): {specificity:.3f}")

        print("="*60)


def main():
    """
    Main function to demonstrate DysplaciaNet evaluation and visualization.
    """
    print("DysplaciaNet Model Evaluation and Visualization")
    print("=" * 50)

    # Initialize evaluator
    evaluator = DysplaciaNetEvaluator()

    # Load model
    model = evaluator.load_model()
    if model is None:
        print("Failed to load model. Please check the model files.")
        return

    # Print model summary
    evaluator.print_model_summary()

    # Note: You need to provide the path to your image folder
    # Update this path to point to your actual image directory
    image_folder_path = input("\nPlease enter the path to your image folder (or press Enter to skip evaluation): ").strip()

    if image_folder_path and os.path.exists(image_folder_path):
        print(f"\nUsing image folder: {image_folder_path}")

        # Evaluate on examples
        metrics = evaluator.evaluate_on_examples(image_folder_path)

        if metrics is not None:
            # Generate detailed report
            evaluator.generate_detailed_report(metrics, image_folder_path)

            # Plot confusion matrix
            evaluator.plot_confusion_matrix(metrics)

            # Plot prediction distribution
            evaluator.plot_prediction_distribution(metrics)

            # Visualize predictions with annotations
            evaluator.visualize_predictions_with_annotations(image_folder_path, num_examples=6)

    else:
        print("Skipping evaluation - no valid image folder provided.")
        print("You can still use individual methods like:")
        print("  evaluator.make_prediction('path/to/image.jpg')")

    print("\nEvaluation complete!")


if __name__ == "__main__":
    main()
